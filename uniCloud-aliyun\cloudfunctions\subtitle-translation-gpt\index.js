// uniCloud云函数：基于GPT的高质量字幕翻译 - 纯并发处理版本
"use strict";

const createConfig = require("uni-config-center");

// 创建语言配置实例
const languageConfig = createConfig({
  pluginId: "subtitle-language",
});

// 常量配置
const CONFIG = {
  DEFAULT_MODEL: "gpt-5-mini",
  TEMPERATURE: 0.3,
  MAX_TOKENS: 4000,
  API_TIMEOUT: 60000 * 2,
  BATCH_SIZE: 50,
  MAX_RETRIES: 3,
  MAX_CONCURRENT_BATCHES: 10,
};

// ASS字幕样式配置现在通过 uni-config-center/subtitle-language 模块管理

/**
 * 标准化语言标识符
 * @param {string} languageIdentifier - 原始语言标识符
 * @returns {string} 标准化后的语言代码
 */
function normalizeLanguageIdentifier(languageIdentifier) {
  if (!languageIdentifier) return "en"; // 默认英文

  const languageMap = languageConfig.config("languageMap") || {};
  const identifierMap = languageConfig.config("languageIdentifierMap") || {};

  // 直接匹配标准代码
  if (languageMap[languageIdentifier]) {
    return languageIdentifier;
  }

  // 通过映射表转换
  const normalizedCode = identifierMap[languageIdentifier];
  if (normalizedCode) {
    return normalizedCode;
  }

  // 尝试小写匹配
  const lowerCase = languageIdentifier.toLowerCase();
  const lowerCaseMatch = identifierMap[lowerCase];
  if (lowerCaseMatch) {
    return lowerCaseMatch;
  }

  console.warn(`未知的语言标识符: ${languageIdentifier}，使用默认值 en`);
  return "en"; // 默认返回英文
}

/**
 * 并发翻译字幕条目 - 使用Promise.all
 */
async function translateSubtitlesBatchOptimized(
  entries,
  apiKey,
  baseUrl,
  model,
  sourceLanguage,
  targetLanguage
) {
  const translationStartTime = Date.now();
  console.log("开始并发翻译字幕", {
    totalEntries: entries.length,
    batchSize: CONFIG.BATCH_SIZE,
    maxConcurrent: CONFIG.MAX_CONCURRENT_BATCHES,
    sourceLanguage,
    targetLanguage,
    model,
  });

  // 过滤有效文本条目
  const validEntries = [];
  entries.forEach((entry, index) => {
    if (entry.text?.trim()) {
      validEntries.push({ ...entry, originalIndex: index });
    }
  });

  if (validEntries.length === 0) {
    console.log("没有有效的字幕文本需要翻译");
    return entries;
  }

  console.log(`有效字幕条目: ${validEntries.length}/${entries.length}`);

  // 将有效条目分批处理
  const batches = [];
  for (let i = 0; i < validEntries.length; i += CONFIG.BATCH_SIZE) {
    batches.push(validEntries.slice(i, i + CONFIG.BATCH_SIZE));
  }

  console.log(`分为 ${batches.length} 批处理，每批最多 ${CONFIG.BATCH_SIZE} 条`);

  // 复制原数组用于存储翻译结果
  const translatedEntries = [...entries];

  // 纯并发处理
  console.log(`启用纯并发处理，最大并发数: ${CONFIG.MAX_CONCURRENT_BATCHES}`);
  const totalTranslatedCount = await processBatchesConcurrently(
    batches,
    translatedEntries,
    apiKey,
    baseUrl,
    model,
    targetLanguage
  );

  const totalTime = (Date.now() - translationStartTime) / 1000;
  console.log(`并发翻译完成`, {
    totalEntries: entries.length,
    validEntries: validEntries.length,
    translatedCount: totalTranslatedCount,
    batchCount: batches.length,
    processingTime: `${totalTime.toFixed(2)}秒`,
    successRate: `${((totalTranslatedCount / validEntries.length) * 100).toFixed(1)}%`,
  });

  return translatedEntries;
}

/**
 * 并发处理多个批次 - 使用Promise.all
 */
async function processBatchesConcurrently(
  batches,
  translatedEntries,
  apiKey,
  baseUrl,
  model,
  targetLanguage
) {
  let totalTranslatedCount = 0;
  const maxConcurrent = Math.min(CONFIG.MAX_CONCURRENT_BATCHES, batches.length);

  // 将批次分组，每组最多包含 maxConcurrent 个批次
  for (let i = 0; i < batches.length; i += maxConcurrent) {
    const batchGroup = batches.slice(i, i + maxConcurrent);
    const groupStartTime = Date.now();

    console.log(
      `并发处理第 ${i + 1}-${Math.min(i + maxConcurrent, batches.length)} 批次（共 ${
        batchGroup.length
      } 个并发）`
    );

    // 创建并发任务 - 使用Promise.all
    const concurrentTasks = batchGroup.map(async (batch, groupIndex) => {
      const actualBatchIndex = i + groupIndex + 1;
      try {
        const batchResult = await translateBatchWithRetry(
          batch,
          apiKey,
          baseUrl,
          model,
          targetLanguage,
          actualBatchIndex
        );
        return { success: true, batchResult, batch, batchIndex: actualBatchIndex };
      } catch (error) {
        return { success: false, error, batch, batchIndex: actualBatchIndex };
      }
    });

    // 等待当前组的所有批次完成
    const results = await Promise.all(concurrentTasks);

    // 处理结果
    results.forEach(({ success, batchResult, batch, batchIndex, error }) => {
      if (success) {
        // 将翻译结果合并到最终数组中
        batchResult.forEach((translatedEntry) => {
          if (translatedEntry.originalIndex !== undefined) {
            translatedEntries[translatedEntry.originalIndex] = translatedEntry;
            totalTranslatedCount++;
          }
        });
        console.log(`第 ${batchIndex} 批翻译完成`);
      } else {
        console.error(`第 ${batchIndex} 批翻译失败:`, error.message);
        // 批次失败时保留原文
        batch.forEach((entry) => {
          if (entry.originalIndex !== undefined) {
            translatedEntries[entry.originalIndex] = entry;
          }
        });
      }
    });

    const groupTime = (Date.now() - groupStartTime) / 1000;
    console.log(`并发组处理完成，耗时: ${groupTime.toFixed(2)}秒`);
  }

  return totalTranslatedCount;
}

/**
 * 带重试机制的单批次翻译函数
 */
async function translateBatchWithRetry(batch, apiKey, baseUrl, model, targetLanguage, batchNumber) {
  let lastError = null;

  for (let attempt = 1; attempt <= CONFIG.MAX_RETRIES; attempt++) {
    try {
      console.log(`第 ${batchNumber} 批第 ${attempt} 次尝试翻译...`);

      const result = await translateSingleBatch(batch, apiKey, baseUrl, model, targetLanguage);

      console.log(`第 ${batchNumber} 批第 ${attempt} 次尝试成功`);
      return result;
    } catch (error) {
      lastError = error;
      console.error(`第 ${batchNumber} 批第 ${attempt} 次尝试失败:`, error.message);

      if (attempt < CONFIG.MAX_RETRIES) {
        console.log(`立即重试第 ${attempt + 1} 次...`);
      }
    }
  }

  // 所有重试都失败，抛出最后一个错误
  throw new Error(
    `第 ${batchNumber} 批翻译失败，已重试 ${CONFIG.MAX_RETRIES} 次: ${lastError.message}`
  );
}

/**
 * 翻译单个批次的字幕条目
 */
async function translateSingleBatch(batch, apiKey, baseUrl, model, targetLanguage) {
  // 构建批次文本
  const batchTexts = batch.map((entry, index) => `${index + 1}. ${entry.text.trim()}`);
  const combinedText = batchTexts.join("\n");

  console.log(`批次文本长度: ${combinedText.length} 字符，包含 ${batch.length} 条字幕`);

  // 获取目标语言名称
  const languageMap = languageConfig.config("languageMap") || {};
  const targetLangName = languageMap[targetLanguage] || targetLanguage.toUpperCase();

  // 构建翻译请求
  const requestBody = {
    model,
    messages: [
      {
        role: "system",
        content: `你是一位资深的多语言翻译专家，将带编号文本翻译成地道流畅的${targetLangName}。要求：
1. 保持编号格式，逐行对应翻译，忠实原意，自然表达
2. 考虑字幕显示特点，控制译文长度适合屏幕显示
3. 对于过长的译文，在合适的语义断点处用\\N进行换行
4. 中文字幕单行建议不超过15个字符，英文不超过20个词
5. 换行时优先在标点符号、连词或语义完整处断开
6. 仅输出译文，不要添加额外说明`,
      },
      {
        role: "user",
        content: `翻译成${targetLangName}：\n\n${combinedText}`,
      },
    ],
    temperature: CONFIG.TEMPERATURE,
    max_completion_tokens: CONFIG.MAX_TOKENS,
  };

  // 调用GPT API
  const apiStartTime = Date.now();
  const response = await uniCloud.httpclient.request(`${baseUrl}/v1/chat/completions`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${apiKey}`,
      "Content-Type": "application/json",
    },
    data: JSON.stringify(requestBody),
    dataType: "json",
    timeout: CONFIG.API_TIMEOUT,
  });

  const apiTime = (Date.now() - apiStartTime) / 1000;
  console.log(`API响应耗时: ${apiTime.toFixed(2)}秒`);

  if (response.status !== 200) {
    let errorMessage = `GPT API请求失败，状态码: ${response.status}`;
    if (response.data?.error?.message) {
      errorMessage += ` - ${response.data.error.message}`;
    }
    throw new Error(errorMessage);
  }

  const result = response.data;
  if (!result.choices?.length || !result.choices[0].message?.content) {
    throw new Error("GPT API返回空结果或格式错误");
  }

  const translatedText = result.choices[0].message.content.trim();
  if (!translatedText) {
    throw new Error("GPT翻译返回空白内容");
  }

  // 解析翻译结果
  const translatedLines = translatedText
    .split("\n")
    .map((line) => line.trim())
    .filter((line) => line);

  console.log(`解析翻译结果，共 ${translatedLines.length} 行`);

  // 匹配翻译结果到原始条目
  const translatedBatch = [...batch];
  batch.forEach((entry, index) => {
    const targetPattern = `${index + 1}.`;
    let translatedLine = null;

    // 查找对应的翻译
    for (const line of translatedLines) {
      if (line.startsWith(targetPattern)) {
        translatedLine = line.substring(targetPattern.length).trim();
        break;
      }
    }

    if (translatedLine) {
      translatedBatch[index] = {
        ...entry,
        text: translatedLine,
      };
    }
    // 如果没找到翻译，保留原文（translatedBatch[index] 已经是原始条目）
  });

  return translatedBatch;
}

// 辅助函数
function createSuccessResponse(message, data) {
  return { code: 200, message, data, timestamp: new Date().toISOString() };
}

function createErrorResponse(code, message, extra = {}) {
  return { code, message, timestamp: new Date().toISOString(), ...extra };
}

async function validateAndGetTask(tasksCollection, taskId, sourceLanguage) {
  const taskInfo = await tasksCollection.doc(taskId).get();
  if (!taskInfo.data?.length) {
    throw new Error("任务不存在");
  }

  const task = taskInfo.data[0];
  if (!task.subtitleOssUrl) {
    throw new Error("缺少字幕文件地址");
  }

  // 标准化语言标识符
  const rawSourceLanguage =
    sourceLanguage === "auto" ? task.detectedLanguage || "en" : sourceLanguage;
  const actualSourceLanguage = normalizeLanguageIdentifier(rawSourceLanguage);

  console.log(`语言标识符标准化: ${rawSourceLanguage} -> ${actualSourceLanguage}`);
  return { task, actualSourceLanguage };
}

async function getAndValidateGptConfig() {
  const gptConfig = createConfig({
    pluginId: "openai-api",
    defaultConfig: {
      baseUrl: "https://aihubmix.com",
      model: CONFIG.DEFAULT_MODEL,
    },
  });

  const apiKey = gptConfig.config("apiKey");
  const baseUrl = gptConfig.config("baseUrl");

  if (!apiKey) {
    throw new Error("GPT API配置缺失，请检查apiKey");
  }

  return { apiKey, baseUrl };
}

async function downloadSrtFromOSS(ossUrl) {
  if (!ossUrl || typeof ossUrl !== "string") {
    throw new Error(`无效的OSS URL: ${ossUrl}`);
  }

  const response = await uniCloud.httpclient.request(ossUrl, {
    method: "GET",
    timeout: 30000,
  });

  if (response.status !== 200) {
    throw new Error(`下载SRT文件失败，状态码: ${response.status}`);
  }

  let srtContent = response.data;
  if (Buffer.isBuffer(srtContent)) {
    srtContent = srtContent.toString("utf8");
  } else if (typeof srtContent !== "string") {
    srtContent = String(srtContent || "");
  }

  if (!srtContent.trim()) {
    throw new Error("下载的SRT文件内容为空");
  }

  return srtContent;
}

function parseSRT(srtContent) {
  if (!srtContent || !srtContent.trim()) {
    throw new Error("SRT字幕内容为空");
  }

  const entries = [];
  const blocks = srtContent.trim().split(/\n\s*\n/);

  for (const block of blocks) {
    if (!block?.trim()) continue;

    const lines = block.trim().split("\n");
    if (lines.length >= 3) {
      const index = parseInt(lines[0]);
      const timeRange = lines[1];
      const text = lines.slice(2).join("\n").trim();

      if (!isNaN(index) && timeRange && text) {
        entries.push({ index, timeRange, text });
      }
    }
  }

  console.log(`SRT解析完成，共 ${entries.length} 条有效字幕`);
  return entries;
}

/**
 * 生成ASS字幕格式（完全复用现有entries结构）
 * @param {Array} entries - 复用parseSRT()返回的字幕条目数组
 * @param {string} targetLanguage - 复用现有目标语言参数
 * @param {Object} styleOverrides - 可选的样式覆盖配置
 * @param {Object} videoResolution - 视频分辨率信息 {width, height}
 * @returns {string} ASS格式字符串
 */
function generateASS(entries, targetLanguage = "zh", styleOverrides = {}, videoResolution = null) {
  const encoding = getLanguageEncoding(targetLanguage);

  // 获取ASS样式配置
  const assStyleConfig = languageConfig.config("assStyleConfig") || {};
  const baseStyle = assStyleConfig.baseStyle || {};
  const scriptInfo = assStyleConfig.scriptInfo || {};

  // 获取语言特定的 WrapStyle 配置
  const languageWrapStyles = assStyleConfig.languageWrapStyles || {};
  const wrapStyle = languageWrapStyles[targetLanguage] || languageWrapStyles.default || scriptInfo.wrapStyle || 1;

  console.log(`ASS 字幕配置: 语言=${targetLanguage}, WrapStyle=${wrapStyle} (${getWrapStyleDescription(wrapStyle)})`);

  // 合并默认配置和覆盖配置
  const style = { ...baseStyle, ...styleOverrides };

  // 生成分辨率配置 - 优先使用实际视频分辨率
  let resolutionConfig = "";
  if (videoResolution && videoResolution.width && videoResolution.height) {
    resolutionConfig = `PlayResX: ${videoResolution.width}\nPlayResY: ${videoResolution.height}`;
    console.log(`ASS 使用实际视频分辨率: ${videoResolution.width}x${videoResolution.height}`);
  } else if (!scriptInfo.autoAdaptive) {
    resolutionConfig = `PlayResX: 1920\nPlayResY: 1080`;
    console.log("ASS 使用默认分辨率: 1920x1080");
  } else {
    console.log("ASS 使用自适应分辨率模式");
  }
 
  const assHeader = `[Script Info]
Title: ${scriptInfo.title}
ScriptType: ${scriptInfo.scriptType}
WrapStyle: ${wrapStyle}
ScaledBorderAndShadow: ${scriptInfo.scaledBorderAndShadow}
${resolutionConfig}

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,${style.fontName},${style.fontSize},${style.primaryColor},${style.secondaryColor},${style.outlineColor},${style.backColor},${style.bold},${style.italic},${style.underline},${style.strikeOut},${style.scaleX},${style.scaleY},${style.spacing},${style.angle},${style.borderStyle},${style.outline},${style.shadow},${style.alignment},${style.marginL},${style.marginR},${style.marginV},${encoding}`;

  console.log(`开始生成ASS事件，共 ${entries.length} 条字幕`);

  const assEvents = entries
    .map((entry, index) => {
      if (!entry || !entry.text || !entry.timeRange) {
        console.warn(`字幕条目 ${index} 数据不完整:`, entry);
        return null;
      }

      const { startTime, endTime } = convertTimeRange(entry.timeRange);
      // 智能文本换行处理：基于字符宽度的精确计算，支持分辨率自适应
      const processedText = smartTextWrap(entry.text, targetLanguage, videoResolution);

      const dialogueLine = `Dialogue: ${index},${startTime},${endTime},Default,,0,0,0,,${processedText}`;

      // 记录前几条字幕的详细信息
      if (index < 3) {
        console.log(`字幕 ${index}: "${entry.text}" -> "${processedText}"`);
        console.log(`时间: ${entry.timeRange} -> ${startTime} - ${endTime}`);
      }

      return dialogueLine;
    })
    .filter(line => line !== null)
    .join("\n");

  console.log(`ASS事件生成完成，有效字幕 ${assEvents.split('\n').length} 条`);

  return `${assHeader}\n[Events]\nFormat: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n${assEvents}`;
}

/**
 * 转换时间格式（复用现有timeRange格式）
 * @param {string} timeRange - SRT格式的时间范围
 * @returns {Object} ASS格式的开始和结束时间
 */
function convertTimeRange(timeRange) {
  const [start, end] = timeRange.split(" --> ");
  return {
    startTime: convertSRTTimeToASS(start),
    endTime: convertSRTTimeToASS(end),
  };
}

/**
 * 将SRT时间格式转换为ASS时间格式
 * @param {string} srtTime - SRT时间格式 (HH:MM:SS,mmm)
 * @returns {string} ASS时间格式 (H:MM:SS.cc)
 */
function convertSRTTimeToASS(srtTime) {
  const [time, milliseconds] = srtTime.split(",");
  const centiseconds = Math.floor(parseInt(milliseconds) / 10);
  const [hours, minutes, seconds] = time.split(":");
  return `${parseInt(hours)}:${minutes}:${seconds}.${centiseconds.toString().padStart(2, "0")}`;
}

/**
 * 获取语言编码（简化版）
 * @param {string} targetLanguage - 目标语言代码
 * @returns {number} 对应的字符编码
 */
function getLanguageEncoding(targetLanguage) {
  const encodings = languageConfig.config("languageEncodings") || {};
  return encodings[targetLanguage] || 1;
}

/**
 * 获取语言特定字体
 * @param {string} targetLanguage - 目标语言代码
 * @returns {string} 对应的字体名称
 */
function getLanguageFont(targetLanguage) {
  const fonts = languageConfig.config("languageFonts") || { default: "Arial" };
  return fonts[targetLanguage] || fonts.default || "Arial";
}

/**
 * 获取 WrapStyle 的描述信息
 * @param {number} wrapStyle - WrapStyle 值
 * @returns {string} 描述信息
 */
function getWrapStyleDescription(wrapStyle) {
  const descriptions = {
    0: "智能换行，行会被均匀分割",
    1: "行尾单词换行，只有\\N会强制换行",
    2: "不进行单词换行，宽行会延伸到屏幕边缘",
    3: "智能换行，类似样式0但底行更宽"
  };
  return descriptions[wrapStyle] || "未知样式";
}

/**
 * 智能字幕文本换行处理 - 基于字符宽度的精确计算
 * @param {string} text - 原始文本
 * @param {string} targetLanguage - 目标语言
 * @param {Object} videoInfo - 视频信息（可选，用于分辨率自适应）
 * @returns {string} 处理后的文本
 */
function smartTextWrap(text, targetLanguage = "zh", videoInfo = null) {
  if (!text || !text.trim()) return text;

  // 先清理现有的换行符
  let cleanText = text.replace(/\r?\n/g, " ").trim();

  // 如果文本已经包含 \N 换行符（GPT可能已经处理过），直接返回
  if (cleanText.includes("\\N")) {
    return cleanText;
  }

  // 获取语言配置
  const wrapConfig = languageConfig.config("wrapConfig") || {};
  const punctuationRules = languageConfig.config("punctuationRules") || {};

  // 检查是否启用智能换行
  const adaptiveRules = wrapConfig.adaptiveRules || {};
  if (!adaptiveRules.enableSmartWrap) {
    console.log("智能换行已禁用，仅清理现有换行符");
    return cleanText;
  }

  // 检查是否启用基于字符宽度的计算
  const widthCalcConfig = wrapConfig.characterWidthCalculation || {};
  if (widthCalcConfig.enabled) {
    console.log("使用基于字符宽度的精确换行计算");
    return performWidthBasedWrap(cleanText, targetLanguage, wrapConfig, punctuationRules, videoInfo);
  }

  // 回退到基于字符数量的传统计算
  console.log("使用传统的基于字符数量的换行计算");
  return performLengthBasedWrap(cleanText, targetLanguage, wrapConfig, punctuationRules);
}



/**
 * 获取视频分辨率信息（从数据库读取或使用默认值）
 * @param {string} taskId - 任务ID（用于查询视频信息）
 * @param {Object} tasksCollection - 数据库任务集合
 * @returns {Promise<Object>} 包含视频宽度和高度的对象
 */
async function getVideoResolution(taskId, tasksCollection) {
  try {
    if (taskId && tasksCollection) {
      // 从数据库查询视频分辨率信息
      const taskResult = await tasksCollection.doc(taskId).get();
      if (taskResult.data && taskResult.data.length > 0) {
        const task = taskResult.data[0];
        if (task.videoWidth && task.videoHeight) {
          console.log(`📹 从数据库获取视频分辨率: ${task.videoWidth}x${task.videoHeight}`);
          console.log(
            `📊 视频类型判断: ${
              task.videoHeight > task.videoWidth
                ? "竖屏"
                : task.videoWidth === task.videoHeight
                ? "正方形"
                : "横屏"
            }`
          );
          console.log(
            `🎯 匹配维度: ${Math.min(task.videoWidth, task.videoHeight)}px (用于分辨率等级判断)`
          );
          return {
            width: task.videoWidth,
            height: task.videoHeight,
            source: "database",
          };
        }
      }
    }
  } catch (error) {
    console.warn("从数据库获取视频分辨率失败:", error.message);
  }

  // 返回常见的1080p作为默认值
  console.log("使用默认视频分辨率: 1920x1080");
  return {
    width: 1920,
    height: 1080,
    source: "default", // 标记这是默认值而非实际检测值
  };
}

/**
 * 全新的视频分辨率分析器 - 基于业界标准重新设计
 * @param {number} videoWidth - 视频宽度
 * @param {number} videoHeight - 视频高度
 * @returns {Object} 详细的分辨率分析结果
 */
function analyzeVideoResolution(videoWidth, videoHeight) {
  if (!videoWidth || !videoHeight) {
    console.log(`⚠️  视频分辨率信息缺失，使用默认分析结果`);
    return {
      width: 1920,
      height: 1080,
      aspectRatio: 16/9,
      orientation: "landscape",
      category: "fhd",
      pixelCount: 1920 * 1080,
      isStandard: true,
      displayName: "1080p (默认)"
    };
  }

  const aspectRatio = videoWidth / videoHeight;
  const pixelCount = videoWidth * videoHeight;

  // 确定视频方向
  let orientation;
  if (aspectRatio > 1.1) {
    orientation = "landscape"; // 横屏
  } else if (aspectRatio < 0.9) {
    orientation = "portrait";  // 竖屏
  } else {
    orientation = "square";    // 正方形
  }

  // 基于像素总数和常见分辨率进行精确分类
  let category, displayName, isStandard = false;

  // 标准分辨率检测（允许±5%的误差）
  const standardResolutions = [
    { width: 640, height: 480, name: "480p", category: "sd" },
    { width: 854, height: 480, name: "480p Wide", category: "sd" },
    { width: 1280, height: 720, name: "720p", category: "hd" },
    { width: 1920, height: 1080, name: "1080p", category: "fhd" },
    { width: 2560, height: 1440, name: "1440p", category: "qhd" },
    { width: 3840, height: 2160, name: "4K", category: "uhd" },
    { width: 7680, height: 4320, name: "8K", category: "uhd8k" },
    // 竖屏标准分辨率
    { width: 720, height: 1280, name: "720p 竖屏", category: "hd" },
    { width: 1080, height: 1920, name: "1080p 竖屏", category: "fhd" },
    { width: 1440, height: 2560, name: "1440p 竖屏", category: "qhd" },
    // 正方形分辨率
    { width: 720, height: 720, name: "720p 正方形", category: "hd" },
    { width: 1080, height: 1080, name: "1080p 正方形", category: "fhd" }
  ];

  // 检查是否匹配标准分辨率
  for (const std of standardResolutions) {
    const widthMatch = Math.abs(videoWidth - std.width) / std.width < 0.05;
    const heightMatch = Math.abs(videoHeight - std.height) / std.height < 0.05;
    if (widthMatch && heightMatch) {
      category = std.category;
      displayName = std.name;
      isStandard = true;
      break;
    }
  }

  // 如果不是标准分辨率，基于像素总数分类
  if (!isStandard) {
    if (pixelCount <= 500000) {
      category = "sd";
      displayName = `${videoWidth}x${videoHeight} (SD级别)`;
    } else if (pixelCount <= 1000000) {
      category = "hd";
      displayName = `${videoWidth}x${videoHeight} (HD级别)`;
    } else if (pixelCount <= 2500000) {
      category = "fhd";
      displayName = `${videoWidth}x${videoHeight} (FHD级别)`;
    } else if (pixelCount <= 4000000) {
      category = "qhd";
      displayName = `${videoWidth}x${videoHeight} (QHD级别)`;
    } else if (pixelCount <= 9000000) {
      category = "uhd";
      displayName = `${videoWidth}x${videoHeight} (4K级别)`;
    } else {
      category = "uhd8k";
      displayName = `${videoWidth}x${videoHeight} (8K级别)`;
    }
  }

  const result = {
    width: videoWidth,
    height: videoHeight,
    aspectRatio: Math.round(aspectRatio * 1000) / 1000,
    orientation,
    category,
    pixelCount,
    isStandard,
    displayName
  };

  console.log(`
🎯 视频分辨率分析结果:
  - 尺寸: ${videoWidth} × ${videoHeight}
  - 宽高比: ${result.aspectRatio}
  - 方向: ${orientation}
  - 分类: ${category}
  - 像素总数: ${pixelCount.toLocaleString()}
  - 标准分辨率: ${isStandard ? '是' : '否'}
  - 显示名称: ${displayName}
  `);

  return result;
}

/**
 * 全新的字幕字体大小计算器 - 基于业界标准重新设计
 * @param {number} videoWidth - 视频宽度
 * @param {number} videoHeight - 视频高度
 * @param {string} targetLanguage - 目标语言
 * @returns {Object} 科学计算的字幕样式配置
 */
function calculateSubtitleStyle(videoWidth, videoHeight, targetLanguage) {
  // 1. 分析视频分辨率
  const videoAnalysis = analyzeVideoResolution(videoWidth, videoHeight);

  // 2. 定义字体大小计算标准（基于业界最佳实践）
  const fontSizeStandards = {
    // 基于屏幕高度的百分比 - 参考广播电视和主流播放器标准
    landscape: {
      sd: 0.045,      // 4.5% 屏幕高度 (480p等)
      hd: 0.042,      // 4.2% 屏幕高度 (720p)
      fhd: 0.040,     // 4.0% 屏幕高度 (1080p)
      qhd: 0.038,     // 3.8% 屏幕高度 (1440p)
      uhd: 0.035,     // 3.5% 屏幕高度 (4K)
      uhd8k: 0.032    // 3.2% 屏幕高度 (8K)
    },
    portrait: {
      sd: 0.038,      // 竖屏字体相对较小
      hd: 0.035,
      fhd: 0.032,
      qhd: 0.030,
      uhd: 0.028,
      uhd8k: 0.025
    },
    square: {
      sd: 0.042,      // 正方形介于横竖屏之间
      hd: 0.039,
      fhd: 0.036,
      qhd: 0.034,
      uhd: 0.031,
      uhd8k: 0.028
    }
  };

  // 3. 边距计算标准
  const marginStandards = {
    landscape: {
      bottom: 0.08,   // 底部边距占屏幕高度8%
      side: 0.05      // 左右边距占屏幕宽度5%
    },
    portrait: {
      bottom: 0.12,   // 竖屏底部边距更大
      side: 0.08      // 竖屏左右边距更大
    },
    square: {
      bottom: 0.10,
      side: 0.06
    }
  };

  // 4. 语言特定调整系数
  const languageAdjustments = {
    zh: { fontScale: 1.0, marginScale: 1.1 },    // 中文需要稍大边距
    ja: { fontScale: 1.0, marginScale: 1.05 },   // 日文
    ko: { fontScale: 1.0, marginScale: 1.02 },   // 韩文
    en: { fontScale: 0.95, marginScale: 1.0 },   // 英文可以稍小
    default: { fontScale: 1.0, marginScale: 1.0 }
  };

  // 5. 计算字体大小
  const fontSizeRatio = fontSizeStandards[videoAnalysis.orientation][videoAnalysis.category];
  const baseFontSize = Math.round(videoAnalysis.height * fontSizeRatio);

  // 6. 应用语言调整
  const langAdjust = languageAdjustments[targetLanguage] || languageAdjustments.default;
  const finalFontSize = Math.round(baseFontSize * langAdjust.fontScale);

  // 7. 计算边距
  const marginStd = marginStandards[videoAnalysis.orientation];
  const bottomMargin = Math.round(videoAnalysis.height * marginStd.bottom * langAdjust.marginScale);
  const sideMargin = Math.round(videoAnalysis.width * marginStd.side);

  // 8. 宽高比特殊调整
  let aspectRatioAdjustment = 1.0;
  if (videoAnalysis.orientation === "landscape") {
    if (videoAnalysis.aspectRatio > 2.0) {
      // 超宽屏（如21:9）字体稍小
      aspectRatioAdjustment = 0.9;
    } else if (videoAnalysis.aspectRatio < 1.5) {
      // 接近正方形的横屏，字体稍大
      aspectRatioAdjustment = 1.1;
    }
  }

  const adjustedFontSize = Math.round(finalFontSize * aspectRatioAdjustment);

  // 9. 安全范围检查
  const minFontSize = Math.max(16, Math.round(videoAnalysis.height * 0.02)); // 最小2%屏幕高度
  const maxFontSize = Math.round(videoAnalysis.height * 0.08); // 最大8%屏幕高度
  const safeFontSize = Math.max(minFontSize, Math.min(maxFontSize, adjustedFontSize));

  const result = {
    fontSize: safeFontSize,
    marginV: bottomMargin,
    marginL: sideMargin,
    marginR: sideMargin,
    videoAnalysis: videoAnalysis,
    calculations: {
      baseFontSizeRatio: fontSizeRatio,
      baseFontSize: baseFontSize,
      languageAdjustment: langAdjust,
      aspectRatioAdjustment: aspectRatioAdjustment,
      finalCalculated: adjustedFontSize,
      safetyClamped: safeFontSize
    }
  };

  console.log(`
🎨 全新字幕样式计算结果:
📹 视频分析: ${videoAnalysis.displayName} (${videoAnalysis.orientation})
📏 宽高比: ${videoAnalysis.aspectRatio} | 像素: ${videoAnalysis.pixelCount.toLocaleString()}

🔢 字体大小计算:
  - 基础比例: ${(fontSizeRatio * 100).toFixed(1)}% 屏幕高度
  - 基础大小: ${baseFontSize}px
  - 语言调整: ×${langAdjust.fontScale} (${targetLanguage})
  - 宽高比调整: ×${aspectRatioAdjustment}
  - 计算结果: ${adjustedFontSize}px
  - 安全范围: ${minFontSize}-${maxFontSize}px
  - 最终大小: ${safeFontSize}px

📐 边距计算:
  - 底部边距: ${bottomMargin}px (${(marginStd.bottom * 100).toFixed(1)}% 屏幕高度)
  - 左右边距: ${sideMargin}px (${(marginStd.side * 100).toFixed(1)}% 屏幕宽度)
  - 语言边距调整: ×${langAdjust.marginScale}

✨ 设计原则: 基于广播电视标准和主流播放器最佳实践
  `);

  return result;
}

/**
 * 重新设计的字幕样式生成器 - 使用全新计算逻辑
 * @param {string} targetLanguage - 目标语言代码
 * @param {number} videoWidth - 视频宽度
 * @param {number} videoHeight - 视频高度
 * @returns {Object} 完整的字幕样式配置
 */
function getLanguageSpecificStyle(targetLanguage, videoWidth = null, videoHeight = null) {
  // 使用全新的计算逻辑
  const styleCalc = calculateSubtitleStyle(videoWidth, videoHeight, targetLanguage);

  // 获取语言特定字体
  const languageFont = getLanguageFont(targetLanguage);

  // 获取基础样式配置
  const assStyleConfig = languageConfig.config("assStyleConfig") || {};
  const baseStyle = assStyleConfig.baseStyle || {};

  // 返回完整样式配置
  const finalStyle = {
    ...baseStyle,
    fontName: languageFont,
    fontSize: styleCalc.fontSize,
    marginL: styleCalc.marginL,
    marginR: styleCalc.marginR,
    marginV: styleCalc.marginV,
  };

  console.log(`
🎯 最终字幕样式配置:
  - 字体: ${languageFont.split(",")[0]}
  - 大小: ${finalStyle.fontSize}px
  - 边距: L${finalStyle.marginL} R${finalStyle.marginR} V${finalStyle.marginV}
  - 视频: ${styleCalc.videoAnalysis.displayName}
  - 语言: ${targetLanguage}
  `);

  return finalStyle;
}

async function uploadTranslatedAssToOSS(taskId, assContent) {
  const OSS = require("ali-oss");

  const aliyunConfig = createConfig({
    pluginId: "aliyun-oss",
    defaultConfig: {
      region: "oss-cn-shanghai",
      bucket: "video--tanslate",
    },
  });

  const accessKeyId = aliyunConfig.config("accessKeyId");
  const accessKeySecret = aliyunConfig.config("accessKeySecret");
  const region = aliyunConfig.config("region");
  const bucketName = aliyunConfig.config("bucket");

  if (!accessKeyId || !accessKeySecret) {
    throw new Error("阿里云OSS配置缺失");
  }

  const client = new OSS({
    accessKeyId,
    accessKeySecret,
    bucket: bucketName,
    region,
  });

  const timestamp = Date.now();
  const objectKey = `subtitle/task_${taskId}_translated_${timestamp}.ass`;

  const uploadResult = await client.put(objectKey, Buffer.from(assContent, "utf8"), {
    headers: {
      "Content-Type": "text/plain; charset=utf-8",
    },
  });

  return {
    subtitleOssUrl: uploadResult.url,
    objectKey,
  };
}



exports.main = async (event) => {
  const startTime = Date.now();

  try {
    const { taskId, sourceLanguage = "auto", targetLanguage = "zh" } = event;

    // 标准化目标语言标识符
    const normalizedTargetLanguage = normalizeLanguageIdentifier(targetLanguage);

    console.log("subtitle-translation-gpt 云函数启动（纯并发处理版本）");
    console.log("输入参数：", { taskId, sourceLanguage, targetLanguage: normalizedTargetLanguage });
    if (targetLanguage !== normalizedTargetLanguage) {
      console.log(`目标语言标准化: ${targetLanguage} -> ${normalizedTargetLanguage}`);
    }

    if (!taskId) {
      return createErrorResponse(400, "缺少必要参数：taskId");
    }

    // 获取数据库引用
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");

    // 验证和获取任务信息
    const { task, actualSourceLanguage } = await validateAndGetTask(
      tasksCollection,
      taskId,
      sourceLanguage
    );

    // 检查是否需要翻译
    if (actualSourceLanguage === normalizedTargetLanguage) {
      console.log(
        `源语言(${actualSourceLanguage})和目标语言(${normalizedTargetLanguage})相同，跳过翻译但继续处理字幕文件`
      );

      // 即使语言相同，也需要完成完整流程：下载、解析、生成ASS、上传、更新状态
      console.log("开始下载字幕文件...");
      const srtContent = await downloadSrtFromOSS(task.subtitleOssUrl);

      console.log("解析SRT字幕文件...");
      const subtitleEntries = parseSRT(srtContent);
      if (subtitleEntries.length === 0) {
        throw new Error("字幕文件为空或格式错误");
      }
      console.log(`解析完成，共 ${subtitleEntries.length} 条字幕`);

      // 生成和上传ASS字幕文件（使用原始字幕，无需翻译）
      console.log("生成并上传字幕文件...");

      // 获取视频分辨率信息
      const videoResolution = await getVideoResolution(taskId, tasksCollection);
      const languageSpecificStyle = getLanguageSpecificStyle(
        normalizedTargetLanguage,
        videoResolution.width,
        videoResolution.height
      );
      console.log(
        `使用${normalizedTargetLanguage}语言优化样式: 动态字体${languageSpecificStyle.fontSize}px, 边距${languageSpecificStyle.marginV}px, 分辨率${videoResolution.width}x${videoResolution.height}(${videoResolution.source})`
      );

      const assContent = generateASS(
        subtitleEntries, // 使用原始字幕，无需翻译
        normalizedTargetLanguage,
        languageSpecificStyle,
        videoResolution
      );
      const uploadResult = await uploadTranslatedAssToOSS(taskId, assContent);

      // 更新任务状态为merging，准备字幕烧录
      await tasksCollection.doc(taskId).update({
        status: "merging",
        subtitleOssUrl: uploadResult.subtitleOssUrl,
        translationStarted: false,
        updateTime: new Date(),
      });

      console.log("字幕处理完成，准备启动字幕烧录");

      // 直接启动字幕烧录
      try {
        const mergeResult = await uniCloud.callFunction({
          name: "process-video-task",
          data: { taskId, action: "merge_subtitle" },
        });
        console.log("字幕烧录启动结果：", mergeResult.result);
      } catch (error) {
        console.error("字幕烧录启动异常：", error.message);
      }

      const processingTime = (Date.now() - startTime) / 1000;
      console.log(`字幕处理任务完成，耗时: ${processingTime.toFixed(2)}秒`);

      return createSuccessResponse("语言相同，字幕处理成功", {
        taskId,
        status: "completed",
        reason: "same_language",
        translatedCount: subtitleEntries.length,
        subtitleOssUrl: uploadResult.subtitleOssUrl,
        processingTime,
        sourceLanguage: actualSourceLanguage,
        targetLanguage: normalizedTargetLanguage,
      });
    }

    console.log(`开始翻译流程：${actualSourceLanguage} -> ${normalizedTargetLanguage}`);

    // 获取和验证API配置
    const { apiKey, baseUrl } = await getAndValidateGptConfig();
    const model = CONFIG.DEFAULT_MODEL;

    console.log("GPT配置验证通过", { baseUrl, model, hasApiKey: !!apiKey });

    // 执行翻译流程
    console.log("开始下载字幕文件...");
    const srtContent = await downloadSrtFromOSS(task.subtitleOssUrl);

    console.log("解析SRT字幕文件...：", srtContent);
    const subtitleEntries = parseSRT(srtContent);
    if (subtitleEntries.length === 0) {
      throw new Error("字幕文件为空或格式错误");
    }
    console.log(`解析完成，共 ${subtitleEntries.length} 条字幕`);

    // 执行纯并发翻译
    console.log("开始纯并发翻译...");
    const translatedEntries = await translateSubtitlesBatchOptimized(
      subtitleEntries,
      apiKey,
      baseUrl,
      model,
      actualSourceLanguage,
      normalizedTargetLanguage
    );
    console.log(`翻译完成，共处理 ${translatedEntries.length} 条字幕`);

    // 检查翻译结果的质量
    let validTranslatedCount = 0;
    let emptyTranslatedCount = 0;
    translatedEntries.forEach((entry, index) => {
      if (entry && entry.text && entry.text.trim()) {
        validTranslatedCount++;
        // 记录前几条翻译结果
        if (index < 3) {
          console.log(`翻译结果 ${index}: "${entry.text}"`);
        }
      } else {
        emptyTranslatedCount++;
        if (index < 3) {
          console.warn(`翻译结果 ${index} 为空或无效:`, entry);
        }
      }
    });

    console.log(`翻译质量检查: 有效 ${validTranslatedCount} 条, 无效 ${emptyTranslatedCount} 条`);

    // 生成和上传翻译后的ASS
    console.log("生成并上传翻译后的字幕文件...");

    // 获取视频分辨率信息
    const videoResolution = await getVideoResolution(taskId, tasksCollection);
    const languageSpecificStyle = getLanguageSpecificStyle(
      normalizedTargetLanguage,
      videoResolution.width,
      videoResolution.height
    );
    console.log(
      `使用${normalizedTargetLanguage}语言优化样式: 动态字体${languageSpecificStyle.fontSize}px, 边距${languageSpecificStyle.marginV}px, 亮黄色字体+黑色边框, 分辨率${videoResolution.width}x${videoResolution.height}(${videoResolution.source})`
    );
    const translatedAssContent = generateASS(
      translatedEntries,
      normalizedTargetLanguage,
      languageSpecificStyle,
      videoResolution
    );

    // 检查ASS文件内容
    console.log(`ASS文件长度: ${translatedAssContent.length} 字符`);
    const dialogueLines = translatedAssContent.split('\n').filter(line => line.startsWith('Dialogue:'));
    console.log(`ASS文件包含 ${dialogueLines.length} 条对话行`);

    // 显示前几行对话内容
    dialogueLines.slice(0, 3).forEach((line, index) => {
      console.log(`对话行 ${index}: ${line.substring(0, 100)}...`);
    });

    const uploadResult = await uploadTranslatedAssToOSS(taskId, translatedAssContent);

    // 更新任务状态为merging，准备字幕烧录
    await tasksCollection.doc(taskId).update({
      status: "merging",
      subtitleOssUrl: uploadResult.subtitleOssUrl,
      translationStarted: false,
      updateTime: new Date(),
    });

    console.log("翻译完成，准备启动字幕烧录");

    // 直接启动字幕烧录
    try {
      const mergeResult = await uniCloud.callFunction({
        name: "process-video-task",
        data: { taskId, action: "merge_subtitle" },
      });
      console.log("字幕烧录启动结果：", mergeResult.result);
    } catch (error) {
      console.error("字幕烧录启动异常：", error.message);
    }

    const processingTime = (Date.now() - startTime) / 1000;
    console.log(`字幕翻译任务完成，耗时: ${processingTime.toFixed(2)}秒`);

    return createSuccessResponse("字幕翻译成功", {
      taskId,
      status: "completed",
      translatedCount: translatedEntries.length,
      subtitleOssUrl: uploadResult.subtitleOssUrl,
      processingTime,
      sourceLanguage: actualSourceLanguage,
      targetLanguage: normalizedTargetLanguage,
    });
  } catch (error) {
    const processingTime = (Date.now() - startTime) / 1000;
    console.error("subtitle-translation-gpt 云函数执行错误：", {
      error: error.message,
      stack: error.stack,
      processingTime: `${processingTime.toFixed(2)}秒`,
    });

    return createErrorResponse(500, "字幕翻译失败: " + error.message, {
      processingTime,
    });
  }
};

/**
 * 基于字符宽度的精确换行处理
 * @param {string} text - 文本
 * @param {string} targetLanguage - 目标语言
 * @param {Object} wrapConfig - 换行配置
 * @param {Object} punctuationRules - 标点符号规则
 * @param {Object} videoInfo - 视频信息
 * @returns {string} 处理后的文本
 */
function performWidthBasedWrap(text, targetLanguage, wrapConfig, punctuationRules, videoInfo) {
  const widthCalcConfig = wrapConfig.characterWidthCalculation || {};
  const fontMetrics = widthCalcConfig.fontMetrics || {};
  const resolutionConfig = widthCalcConfig.resolutionAdaptive || {};

  // 计算文本的实际显示宽度
  const textWidth = calculateTextDisplayWidth(text, fontMetrics);

  // 获取最大允许宽度
  const maxWidth = getMaxAllowedWidth(videoInfo, resolutionConfig, targetLanguage);

  console.log(`文本显示宽度: ${textWidth.toFixed(2)}, 最大允许宽度: ${maxWidth.toFixed(2)}`);

  // 如果文本宽度在允许范围内，不需要换行
  if (textWidth <= maxWidth) {
    console.log("文本宽度适中，无需换行");
    return text;
  }

  // 执行基于宽度的智能换行
  console.log("文本过宽，执行基于宽度的智能换行");
  return performWidthBasedBreaking(text, targetLanguage, maxWidth, fontMetrics, punctuationRules);
}

/**
 * 传统的基于字符数量的换行处理（回退方案）
 * @param {string} text - 文本
 * @param {string} targetLanguage - 目标语言
 * @param {Object} wrapConfig - 换行配置
 * @param {Object} punctuationRules - 标点符号规则
 * @returns {string} 处理后的文本
 */
function performLengthBasedWrap(text, targetLanguage, wrapConfig, punctuationRules) {
  const cjkLanguages = wrapConfig.cjkLanguages || ["zh", "zh-cn", "zh-tw", "ja", "ko"];
  const isCJK = cjkLanguages.includes(targetLanguage);

  // 从配置中获取长度阈值，如果没有配置则使用默认值
  const configThresholds = wrapConfig.lengthThresholds || {};
  const defaultThresholds = {
    cjk: {
      singleLine: 15,    // 中日韩文字单行建议长度
      maxLine: 20,       // 最大单行长度
      preferredTotal: 25 // 总长度超过此值时考虑换行
    },
    western: {
      singleLine: 20,    // 西文单行建议长度（更适合字幕显示）
      maxLine: 30,       // 最大单行长度
      preferredTotal: 35 // 总长度超过此值时考虑换行
    }
  };

  const cjkThreshold = { ...defaultThresholds.cjk, ...configThresholds.cjk };
  const westernThreshold = { ...defaultThresholds.western, ...configThresholds.western };
  const threshold = isCJK ? cjkThreshold : westernThreshold;

  const textLength = isCJK ? text.length : text.split(/\s+/).length;

  // 如果文本长度在合理范围内，不需要换行
  if (textLength <= threshold.singleLine) {
    console.log(`字幕长度适中(${textLength}/${threshold.singleLine})，无需换行: ${text.substring(0, 30)}...`);
    return text;
  }

  // 如果文本过长，尝试智能换行
  if (textLength > threshold.preferredTotal) {
    console.log(`字幕过长(${textLength}/${threshold.preferredTotal})，执行智能换行: ${text.substring(0, 30)}...`);
    const wrappedText = performIntelligentWrap(text, threshold, isCJK, punctuationRules);
    console.log(`换行结果: ${wrappedText}`);
    return wrappedText;
  }

  console.log(`字幕长度中等(${textLength})，保持原样: ${text.substring(0, 30)}...`);
  return text;
}

/**
 * 计算文本的实际显示宽度
 * @param {string} text - 文本内容
 * @param {Object} fontMetrics - 字体度量配置
 * @returns {number} 文本显示宽度（以基准字符宽度为单位）
 */
function calculateTextDisplayWidth(text, fontMetrics) {
  if (!text) return 0;

  // 获取字符宽度配置
  const cjkMetrics = fontMetrics.cjk || { widthRatio: 1.0 };
  const westernMetrics = fontMetrics.western || { widthRatio: 0.5 };
  const punctuationMetrics = fontMetrics.punctuation || {};

  const halfWidthPunctuation = punctuationMetrics.halfWidth || [];
  const fullWidthPunctuation = punctuationMetrics.fullWidth || [];

  let totalWidth = 0;

  for (let i = 0; i < text.length; i++) {
    const char = text[i];
    const charCode = char.charCodeAt(0);

    // 判断字符类型并计算宽度
    if (fullWidthPunctuation.includes(char)) {
      // 全角标点符号
      totalWidth += cjkMetrics.widthRatio;
    } else if (halfWidthPunctuation.includes(char)) {
      // 半角标点符号
      totalWidth += westernMetrics.widthRatio;
    } else if (isCJKCharacter(charCode)) {
      // CJK字符
      totalWidth += cjkMetrics.widthRatio;
    } else if (char === ' ') {
      // 空格
      totalWidth += westernMetrics.widthRatio * 0.5;
    } else {
      // 西文字符
      totalWidth += westernMetrics.widthRatio;
    }
  }

  console.log(`文本 "${text.substring(0, 20)}..." 计算宽度: ${totalWidth.toFixed(2)}`);
  return totalWidth;
}

/**
 * 判断字符是否为CJK字符
 * @param {number} charCode - 字符编码
 * @returns {boolean} 是否为CJK字符
 */
function isCJKCharacter(charCode) {
  // CJK统一汉字 (U+4E00-U+9FFF)
  // CJK统一汉字扩展A (U+3400-U+4DBF)
  // CJK统一汉字扩展B (U+20000-U+2A6DF)
  // 平假名 (U+3040-U+309F)
  // 片假名 (U+30A0-U+30FF)
  // 韩文音节 (U+AC00-U+D7AF)
  return (
    (charCode >= 0x4E00 && charCode <= 0x9FFF) ||   // CJK统一汉字
    (charCode >= 0x3400 && charCode <= 0x4DBF) ||   // CJK扩展A
    (charCode >= 0x3040 && charCode <= 0x309F) ||   // 平假名
    (charCode >= 0x30A0 && charCode <= 0x30FF) ||   // 片假名
    (charCode >= 0xAC00 && charCode <= 0xD7AF) ||   // 韩文音节
    (charCode >= 0x20000 && charCode <= 0x2A6DF)    // CJK扩展B
  );
}

/**
 * 获取最大允许显示宽度
 * @param {Object} videoInfo - 视频信息
 * @param {Object} resolutionConfig - 分辨率配置
 * @param {string} targetLanguage - 目标语言
 * @returns {number} 最大允许宽度
 */
function getMaxAllowedWidth(videoInfo, resolutionConfig, targetLanguage) {
  // 默认配置
  const defaultConfig = {
    safeAreaPercentage: 0.85,
    resolutionProfiles: {
      "480p": { maxCharsPerLine: 32 },
      "720p": { maxCharsPerLine: 48 },
      "1080p": { maxCharsPerLine: 64 },
      "4k": { maxCharsPerLine: 96 }
    }
  };

  const config = { ...defaultConfig, ...resolutionConfig };
  const safeAreaPercentage = config.safeAreaPercentage || 0.85;

  // 如果有视频信息，根据分辨率动态计算
  if (videoInfo && videoInfo.width && videoInfo.height) {
    const resolution = determineResolutionProfile(videoInfo.width, videoInfo.height);
    const profile = config.resolutionProfiles[resolution];

    if (profile && profile.maxCharsPerLine) {
      const maxWidth = profile.maxCharsPerLine * safeAreaPercentage;
      console.log(`基于视频分辨率 ${videoInfo.width}x${videoInfo.height} (${resolution})，最大宽度: ${maxWidth.toFixed(2)}`);
      return maxWidth;
    }
  }

  // 回退到默认值
  const cjkLanguages = ["zh", "zh-cn", "zh-tw", "ja", "ko"];
  const isCJK = cjkLanguages.includes(targetLanguage);
  const defaultMaxWidth = isCJK ? 32 : 48; // CJK字符相对较少，西文字符相对较多

  console.log(`使用默认最大宽度: ${defaultMaxWidth} (${isCJK ? 'CJK' : 'Western'})`);
  return defaultMaxWidth;
}

/**
 * 确定分辨率档次
 * @param {number} width - 视频宽度
 * @param {number} height - 视频高度
 * @returns {string} 分辨率档次
 */
function determineResolutionProfile(width, height) {
  const minDimension = Math.min(width, height);

  if (minDimension >= 2160) return "4k";
  if (minDimension >= 1080) return "1080p";
  if (minDimension >= 720) return "720p";
  return "480p";
}

/**
 * 执行基于宽度的智能断行
 * @param {string} text - 文本
 * @param {string} targetLanguage - 目标语言
 * @param {number} maxWidth - 最大允许宽度
 * @param {Object} fontMetrics - 字体度量
 * @param {Object} punctuationRules - 标点符号规则
 * @returns {string} 处理后的文本
 */
function performWidthBasedBreaking(text, targetLanguage, maxWidth, fontMetrics, punctuationRules) {
  const cjkLanguages = ["zh", "zh-cn", "zh-tw", "ja", "ko"];
  const isCJK = cjkLanguages.includes(targetLanguage);

  if (isCJK) {
    return performCJKWidthBasedBreaking(text, maxWidth, fontMetrics, punctuationRules);
  } else {
    return performWesternWidthBasedBreaking(text, maxWidth, fontMetrics, punctuationRules);
  }
}

/**
 * CJK文字的基于宽度的断行
 * @param {string} text - 文本
 * @param {number} maxWidth - 最大宽度
 * @param {Object} fontMetrics - 字体度量
 * @param {Object} punctuationRules - 标点符号规则
 * @returns {string} 处理后的文本
 */
function performCJKWidthBasedBreaking(text, maxWidth, fontMetrics, punctuationRules) {
  const cjkRules = punctuationRules.cjk || {};
  const avoidBreakAfter = cjkRules.avoidBreakAfter || [];
  const avoidBreakBefore = cjkRules.avoidBreakBefore || [];
  const preferBreakAfter = cjkRules.preferBreakAfter || [];

  // 寻找最佳断点
  let currentWidth = 0;
  let bestBreakPoint = -1;
  let bestScore = -1;
  const targetWidth = maxWidth / 2; // 目标是平均分配

  for (let i = 0; i < text.length; i++) {
    const char = text[i];
    const charWidth = getCharacterWidth(char, fontMetrics);
    currentWidth += charWidth;

    // 如果当前宽度接近目标宽度，开始寻找断点
    if (currentWidth >= targetWidth * 0.7 && currentWidth <= maxWidth * 0.8) {
      let score = 0;

      // 距离目标宽度越近分数越高
      const distanceFromTarget = Math.abs(currentWidth - targetWidth);
      score += (targetWidth - distanceFromTarget) / targetWidth * 10;

      // 标点符号加分
      if (i > 0) {
        const charBefore = text[i - 1];
        if (preferBreakAfter.includes(charBefore)) {
          score += 15;
        } else if (!avoidBreakAfter.includes(charBefore)) {
          score += 5;
        }
      }

      if (i < text.length - 1) {
        const charAfter = text[i];
        if (avoidBreakBefore.includes(charAfter)) {
          score -= 10;
        }
      }

      if (score > bestScore) {
        bestScore = score;
        bestBreakPoint = i;
      }
    }

    // 如果超过最大宽度，必须断开
    if (currentWidth > maxWidth) {
      break;
    }
  }

  // 执行断行
  if (bestBreakPoint > 0 && bestBreakPoint < text.length - 1) {
    const firstLine = text.substring(0, bestBreakPoint).trim();
    const secondLine = text.substring(bestBreakPoint).trim();

    const firstLineWidth = calculateTextDisplayWidth(firstLine, fontMetrics);
    const secondLineWidth = calculateTextDisplayWidth(secondLine, fontMetrics);

    console.log(`CJK断行: 第一行宽度 ${firstLineWidth.toFixed(2)}, 第二行宽度 ${secondLineWidth.toFixed(2)}`);

    if (firstLineWidth <= maxWidth && secondLineWidth <= maxWidth) {
      return `${firstLine}\\N${secondLine}`;
    }
  }

  // 如果找不到合适的断点，按最大宽度强制断开
  return forceBreakByWidth(text, maxWidth, fontMetrics);
}

/**
 * 西文的基于宽度的断行
 * @param {string} text - 文本
 * @param {number} maxWidth - 最大宽度
 * @param {Object} fontMetrics - 字体度量
 * @param {Object} punctuationRules - 标点符号规则
 * @returns {string} 处理后的文本
 */
function performWesternWidthBasedBreaking(text, maxWidth, fontMetrics, punctuationRules) {
  const words = text.split(/\s+/);
  const westernRules = punctuationRules.western || {};
  const avoidBreakAfter = westernRules.avoidBreakAfter || [];

  let currentWidth = 0;
  let bestBreakPoint = -1;
  let bestScore = -1;
  const targetWidth = maxWidth / 2;

  for (let i = 0; i < words.length; i++) {
    const word = words[i];
    const wordWidth = calculateTextDisplayWidth(word + " ", fontMetrics);
    currentWidth += wordWidth;

    // 如果当前宽度接近目标宽度，开始寻找断点
    if (currentWidth >= targetWidth * 0.7 && currentWidth <= maxWidth * 0.8) {
      let score = 0;

      // 距离目标宽度越近分数越高
      const distanceFromTarget = Math.abs(currentWidth - targetWidth);
      score += (targetWidth - distanceFromTarget) / targetWidth * 10;

      // 避免在小词后断开
      if (i > 0) {
        const prevWord = words[i - 1].toLowerCase();
        if (avoidBreakAfter.includes(prevWord)) {
          score -= 10;
        } else {
          score += 5;
        }
      }

      if (score > bestScore) {
        bestScore = score;
        bestBreakPoint = i;
      }
    }

    // 如果超过最大宽度，必须断开
    if (currentWidth > maxWidth) {
      break;
    }
  }

  // 执行断行
  if (bestBreakPoint > 0 && bestBreakPoint < words.length - 1) {
    const firstLine = words.slice(0, bestBreakPoint).join(" ");
    const secondLine = words.slice(bestBreakPoint).join(" ");

    const firstLineWidth = calculateTextDisplayWidth(firstLine, fontMetrics);
    const secondLineWidth = calculateTextDisplayWidth(secondLine, fontMetrics);

    console.log(`西文断行: 第一行宽度 ${firstLineWidth.toFixed(2)}, 第二行宽度 ${secondLineWidth.toFixed(2)}`);

    if (firstLineWidth <= maxWidth && secondLineWidth <= maxWidth) {
      return `${firstLine}\\N${secondLine}`;
    }
  }

  // 如果找不到合适的断点，按词数平均分配
  const midPoint = Math.floor(words.length / 2);
  const firstLine = words.slice(0, midPoint).join(" ");
  const secondLine = words.slice(midPoint).join(" ");

  return `${firstLine}\\N${secondLine}`;
}

/**
 * 获取单个字符的显示宽度
 * @param {string} char - 字符
 * @param {Object} fontMetrics - 字体度量
 * @returns {number} 字符宽度
 */
function getCharacterWidth(char, fontMetrics) {
  const charCode = char.charCodeAt(0);
  const cjkMetrics = fontMetrics.cjk || { widthRatio: 1.0 };
  const westernMetrics = fontMetrics.western || { widthRatio: 0.5 };
  const punctuationMetrics = fontMetrics.punctuation || {};

  const halfWidthPunctuation = punctuationMetrics.halfWidth || [];
  const fullWidthPunctuation = punctuationMetrics.fullWidth || [];

  if (fullWidthPunctuation.includes(char)) {
    return cjkMetrics.widthRatio;
  } else if (halfWidthPunctuation.includes(char)) {
    return westernMetrics.widthRatio;
  } else if (isCJKCharacter(charCode)) {
    return cjkMetrics.widthRatio;
  } else if (char === ' ') {
    return westernMetrics.widthRatio * 0.5;
  } else {
    return westernMetrics.widthRatio;
  }
}

/**
 * 按最大宽度强制断开文本
 * @param {string} text - 文本
 * @param {number} maxWidth - 最大宽度
 * @param {Object} fontMetrics - 字体度量
 * @returns {string} 处理后的文本
 */
function forceBreakByWidth(text, maxWidth, fontMetrics) {
  let currentWidth = 0;
  let breakPoint = -1;

  for (let i = 0; i < text.length; i++) {
    const char = text[i];
    const charWidth = getCharacterWidth(char, fontMetrics);
    currentWidth += charWidth;

    if (currentWidth >= maxWidth) {
      breakPoint = i;
      break;
    }
  }

  if (breakPoint > 0 && breakPoint < text.length - 1) {
    const firstLine = text.substring(0, breakPoint).trim();
    const secondLine = text.substring(breakPoint).trim();

    console.log(`强制断行: 在位置 ${breakPoint} 断开`);
    return `${firstLine}\\N${secondLine}`;
  }

  return text;
}

/**
 * 传统的智能换行处理（基于字符数量）
 * @param {string} text - 文本
 * @param {Object} threshold - 长度阈值
 * @param {boolean} isCJK - 是否为CJK语言
 * @param {Object} punctuationRules - 标点符号规则
 * @returns {string} 处理后的文本
 */
function performIntelligentWrap(text, threshold, isCJK, punctuationRules) {
  if (isCJK) {
    return wrapCJKText(text, threshold, punctuationRules);
  } else {
    return wrapWesternText(text, threshold, punctuationRules);
  }
}

/**
 * 中日韩文字换行处理
 * @param {string} text - 文本
 * @param {Object} threshold - 长度阈值
 * @param {Object} punctuationRules - 标点符号规则
 * @returns {string} 换行处理后的文本
 */
function wrapCJKText(text, threshold, punctuationRules) {
  const cjkRules = punctuationRules.cjk || {};
  const avoidBreakAfter = cjkRules.avoidBreakAfter || [];
  const avoidBreakBefore = cjkRules.avoidBreakBefore || [];
  const preferBreakAfter = cjkRules.preferBreakAfter || [];

  // 寻找最佳断点
  const midPoint = Math.floor(text.length / 2);
  let bestBreakPoint = -1;
  let bestScore = -1;

  // 在中点附近寻找合适的断点
  const searchRange = Math.min(5, Math.floor(text.length * 0.2));

  for (let i = midPoint - searchRange; i <= midPoint + searchRange; i++) {
    if (i <= 0 || i >= text.length - 1) continue;

    const charBefore = text[i - 1];
    const charAfter = text[i];
    let score = 0;

    // 优先在句号、感叹号、问号后断开
    if (preferBreakAfter.includes(charBefore)) {
      score += 10;
    }

    // 避免在某些标点符号前后断开
    if (avoidBreakAfter.includes(charBefore)) {
      score -= 5;
    }
    if (avoidBreakBefore.includes(charAfter)) {
      score -= 5;
    }

    // 距离中点越近分数越高
    const distanceFromMid = Math.abs(i - midPoint);
    score += (searchRange - distanceFromMid) * 2;

    if (score > bestScore) {
      bestScore = score;
      bestBreakPoint = i;
    }
  }

  // 如果找到合适的断点，进行换行
  if (bestBreakPoint > 0) {
    const firstLine = text.substring(0, bestBreakPoint).trim();
    const secondLine = text.substring(bestBreakPoint).trim();

    // 检查两行长度是否合理
    if (firstLine.length <= threshold.maxLine && secondLine.length <= threshold.maxLine) {
      return `${firstLine}\\N${secondLine}`;
    }
  }

  // 如果没有找到合适的断点，按长度强制断开
  const breakPoint = Math.min(threshold.singleLine, Math.floor(text.length / 2));
  const firstLine = text.substring(0, breakPoint).trim();
  const secondLine = text.substring(breakPoint).trim();

  return `${firstLine}\\N${secondLine}`;
}

/**
 * 西文换行处理
 * @param {string} text - 文本
 * @param {Object} threshold - 长度阈值
 * @param {Object} punctuationRules - 标点符号规则
 * @returns {string} 换行处理后的文本
 */
function wrapWesternText(text, threshold, punctuationRules) {
  const words = text.split(/\s+/);
  const westernRules = punctuationRules.western || {};
  const avoidBreakAfter = westernRules.avoidBreakAfter || [];

  if (words.length <= threshold.singleLine) {
    return text;
  }

  // 寻找最佳断点
  const midPoint = Math.floor(words.length / 2);
  let bestBreakPoint = midPoint;

  // 在中点附近寻找合适的断点
  const searchRange = Math.min(3, Math.floor(words.length * 0.2));

  for (let i = midPoint - searchRange; i <= midPoint + searchRange; i++) {
    if (i <= 0 || i >= words.length - 1) continue;

    const wordBefore = words[i - 1].toLowerCase();

    // 避免在小词后断开
    if (!avoidBreakAfter.includes(wordBefore)) {
      bestBreakPoint = i;
      break;
    }
  }

  const firstLine = words.slice(0, bestBreakPoint).join(" ");
  const secondLine = words.slice(bestBreakPoint).join(" ");

  // 检查两行长度是否合理
  if (firstLine.split(/\s+/).length <= threshold.maxLine &&
      secondLine.split(/\s+/).length <= threshold.maxLine) {
    return `${firstLine}\\N${secondLine}`;
  }

  return text;
}
